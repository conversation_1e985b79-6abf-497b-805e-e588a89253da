INSERT INTO `nic_common`.`lead_partner`(`id_lead_partner`, `name`, `decription`, `check_type`, `partner_identifier`) VALUES (5082, 'RCC', 'Remarketing Control Center', 'ping', NULL);
INSERT INTO `nic_common`.`lead_partner_submitter`(`id_lead_partner_submitter`, `name`, `description`, `url`, `request_timeout`, `lead_content_type`, `lead_send_method`, `lead_send_format`, `authorization_params`, `authorization_type`, `request_field_mapping`, `full_submission_request_template`, `email_from`, `email_to`, `email_subject`, `success_rule`, `failure_rule`, `retry_rule`, `additional_headers`) VALUES (156, 'RCC SMS FHC', 'Remarketing Control Center SMS Leads FHC', 'https://control-center-dev.nationsinfocorp.com/webhook/8a47c83d-15c9-4a4a-a995-299d81a46887', 20000, NULL, 'POST', 'UrlEncodedFormEntity', NULL, NULL, NULL, '\'{\' \n  + \'\"EmailAddress\": \' + #jsonMapper.writeValueAsString(#lead.email == null ? #fields[\'email\'] : #lead.email)\n  + \', \"brand_id\": 80\'\n  + \', \"firstname\": \' + #jsonMapper.writeValueAsString(#lead.firstName == null ? #fields[\'custfullname\'] : #lead.firstName)\n  + \', \"lastname\": \' + #jsonMapper.writeValueAsString(#lead.lastName)\n  + \', \"initial_cancel_date\": \' + #jsonMapper.writeValueAsString(#fields[\'initial_cancel_date\'])\n  + \', \"initial_expiration_date\": \' + #jsonMapper.writeValueAsString(#fields[\'initial_expiration_date\'])\n  + \', \"initial_desc\": \' + #jsonMapper.writeValueAsString(#fields[\'initial_desc\'])\n  + \', \"source\": \' + #jsonMapper.writeValueAsString(#fields[\'source\'])\n  + \', \"adid\": \' + #jsonMapper.writeValueAsString(#lead.idAd)\n  + \', \"pid\": \' + #jsonMapper.writeValueAsString(\"\" + #lead.idPartner)  \n  + \', \"leaddate\": \' + #jsonMapper.writeValueAsString(#lead.getFormattedLeadDate(\"yyyy-MM-dd hh:mm:ss a\"))\n  + \', \"ip_address\": \' + #jsonMapper.writeValueAsString(#lead.customerIp)\n  + \', \"eo_type\": \' + #jsonMapper.writeValueAsString(#lead.leadType)\n  + \', \"affiliate_id\": \' + #jsonMapper.writeValueAsString(#fields[\'affiliate_id\'])\n  + \', \"eo_promotion_id\": \' + #jsonMapper.writeValueAsString(#fields[\'eo_promotion_id\'])\n  + \', \"customer_address_1\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_1\'])\n  + \', \"customer_address_2\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_2\'])\n  + \', \"customer_city\": \' + #jsonMapper.writeValueAsString(#lead.city)\n  + \', \"customer_state\": \' + #jsonMapper.writeValueAsString(#lead.state)\n  + \', \"original_adid\": \' + #jsonMapper.writeValueAsString(#fields[\'original_adid\'])\n  + \', \"initial_signup_date\": \' + #jsonMapper.writeValueAsString(#fields[\'initial_signup_date\'])\n  + \', \"initial_decline_date\": \' + #jsonMapper.writeValueAsString(#fields[\'initial_decline_date\'])\n  + \', \"geo_zip\": \' + #jsonMapper.writeValueAsString(#fields[\'geo_zip\'])\n  + \', \"eo_capture_location\": \' + #jsonMapper.writeValueAsString(#fields[\'eo_capture_location\'])\n  + \', \"registration_step\": \' + #jsonMapper.writeValueAsString(#fields[\'registration_step\'])\n  + \', \"vertical_type\": \' + #jsonMapper.writeValueAsString(#lead.verticalType)\n  + \', \"customer_zip\": \' + #jsonMapper.writeValueAsString(#lead.zip)\n  + \', \"bucket\": \' + #jsonMapper.writeValueAsString(#fields[\'bucket\'])\n  + \', \"zip\": \' + #jsonMapper.writeValueAsString(#lead.zip)\n  + \', \"initial_cancel_number\": \' + #jsonMapper.writeValueAsString(#fields[\'initial_cancel_number\'])\n  + \', \"customer_phone\": \' + #jsonMapper.writeValueAsString(#lead.phone)\n  + \', \"enrollment_status\": \' + #jsonMapper.writeValueAsString(#fields[\'enrollment_status\'])\n  + \', \"verification_status\": \' + #jsonMapper.writeValueAsString(#fields[\'verification_status\'])\n  + \', \"locale\": \"US\"\'\n  + \', \"tcpa_optin\": \' + #jsonMapper.writeValueAsString(#fields[\'tcpaOptin\'] == \'true\' ? 1 : 0)\n  + \', \"lead_modified_date\": \' + #jsonMapper.writeValueAsString(#fields[\'lead_modified_date\'])\n  + \', \"CreditCardSeen\": \' + #jsonMapper.writeValueAsString(#fields[\'CreditCardSeen\'])\n  + \', \"CreditCardInitiated\": \' + #jsonMapper.writeValueAsString(#fields[\'CreditCardInitiated\'])\n  + \', \"Carrier\": \' + #jsonMapper.writeValueAsString(#fields[\'carrier\'])\n  + \', \"CarrierParent\": \' + #jsonMapper.writeValueAsString(#fields[\'parentCarrier\'])\n  + \', \"Blacklisted\": \' + #jsonMapper.writeValueAsString(#fields[\'blacklisted\'])\n  + \', \"Status\": \' + #jsonMapper.writeValueAsString(#fields[\'status\'])\n  + \', \"Type\": \' + #jsonMapper.writeValueAsString(#fields[\'type\'])\n  + \', \"global_uuid\": \' + #jsonMapper.writeValueAsString(#fields[\'global_uuid\'])\n  + \', \"shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'shortURL\'])\n  + \', \"pl_shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'pl_shorturl\'])\n  + \', \"jluvr\": \' + #jsonMapper.writeValueAsString(#lead.jluvr)\n  + \', \"lead_id\" : \' + #jsonMapper.writeValueAsString(\"\" + #lead.id)\n+ \'}\'', NULL, NULL, NULL, '#response.contains(\'success\')', NULL, NULL, '{\"Authorization\":\"Basic bjhuOkV0cGNSTE1rR29CdA==\"}');
INSERT INTO `nic_common`.`lead_partner_campaigns`(`id_lead_partner_campaigns`, `ldm_version`, `id_lead_partner`, `name`, `description`, `status`, `accepting_lead_type`, `max_lead_retries`, `max_lead_age`, `lead_submit_delay`, `lead_submit_delay_type`, `priority`, `ratio`, `exclusivity`, `last_buyer`, `brand_id`, `ad_id`, `partner_id`, `super_partner_id`, `ad_location`, `lead_capture_location`, `capture_location`, `vertical_type`, `submitter`, `id_lead_submitter`, `daytime_restriction`, `tracking`, `url`, `api_variable`, `lead_limit`, `lead_limit_type`, `coverage_type`) VALUES (7654, 3, 5082, 'RCC SMS FHC', 'Remarketing Control Center SMS leads FHC', 'active', 'sms_fhc', 1, 127, 0, 'minutes', 1, NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RCC SMS FHC', 156, 'disabled', NULL, 'defined in submitter', 'defined in submitter', NULL, NULL, NULL);