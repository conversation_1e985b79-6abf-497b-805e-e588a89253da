import psycopg2
import random
import time  # Add this line to import the time module

def build_messages(p_content_type, p_platform, p_start_day, p_end_day):
    # Database connection parameters
    conn_params = {
        'dbname': 'your_database_name',
        'user': 'your_username',
        'password': 'your_password',
        'host': 'your_host',
        'port': 'your_port'
    }

    try:
        # Connect to the database
        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor()

        # Fetch subscriber data
        query = """
        SELECT s.customer_phone, s.emailaddress, s.firstname, s.lastname, s.shorturl,
               b.brand_name, b.vertical
        FROM subscriber.subscriber_sms s
        JOIN subscriber.brand b ON s.brand_id = b.brand_id
        LEFT JOIN subscriber.optout_sms o ON s.customer_phone = o.phone
        WHERE o.phone IS NULL
          AND s.Blacklisted <> 'true'
          AND s.status <> 'not_mobile'
          AND s.leaddate >= CURRENT_DATE - INTERVAL '1 day' * %s
          AND s.leaddate <= CURRENT_DATE - INTERVAL '1 day' * %s
        """
        cursor.execute(query, (p_start_day, p_end_day))
        subscribers = cursor.fetchall()

        # Select random messages
        query = """
        SELECT scs.version, scs.message
        FROM subscriber.content_sms scs
        WHERE scs.vertical = %s
          AND scs.content_type = %s
        ORDER BY RANDOM()
        LIMIT 1
        """
        count_inserted = 0

        for subscriber in subscribers:
            v_phone, v_email, v_firstname, v_lastname, v_shorturl, v_brand_name, v_vertical = subscriber
            cursor.execute(query, (v_vertical, p_content_type))
            message_data = cursor.fetchone()

            if message_data:
                v_version, v_message = message_data

                # Insert into transaction SMS table
                insert_query = """
                INSERT INTO subscriber.transaction_sms(phone, email, firstname, lastname, version, message, platform, campaign_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_DATE)
                """
                cursor.execute(insert_query, (v_phone, v_email, v_firstname, v_lastname, v_version, f"{v_brand_name}: {v_message} {v_shorturl}?A={v_version}&T={hex(int(time.time()))}", p_platform))
                count_inserted += 1

        # Commit the transaction
        conn.commit()

        return f"Successfully inserted {count_inserted} row(s) into transaction_sms."

    except Exception as e:
        return f"Error: {str(e)}"

    finally:
        if conn:
            cursor.close()
            conn.close()

# Example usage
if __name__ == "__main__":
    result = build_messages('sms', 'email', 1, 7)  # Corrected the syntax here
    print(result)
